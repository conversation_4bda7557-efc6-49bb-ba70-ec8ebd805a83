import { useState } from 'react';
import {
  Card,
  Switch,
  Button,
  TimePicker,
  InputNumber,
  Space,
  Typography,
  Grid,
  Message,
} from '@arco-design/web-react';
import {
  IconPlus,
  IconClose,
  IconThunderbolt,
} from '@arco-design/web-react/icon';
import styles from './index.module.less';

const { Title, Text } = Typography;
const { Row, Col } = Grid;

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
  quota: number;
}

interface WeekSchedule {
  [key: string]: {
    enabled: boolean;
    slots: number;
  };
}

export default function ScheduleSettings() {
  // 星期设置状态
  const [weekSchedule, setWeekSchedule] = useState<WeekSchedule>({
    monday: { enabled: true, slots: 7 },
    tuesday: { enabled: true, slots: 7 },
    wednesday: { enabled: true, slots: 7 },
    thursday: { enabled: true, slots: 7 },
    friday: { enabled: true, slots: 7 },
    saturday: { enabled: true, slots: 7 },
    sunday: { enabled: true, slots: 7 },
  });

  // 付费预约时段状态
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([
    { id: '1', startTime: '08:45', endTime: '08:50', quota: 2 },
    { id: '2', startTime: '09:15', endTime: '09:20', quota: 2 },
    { id: '3', startTime: '09:45', endTime: '09:50', quota: 2 },
    { id: '4', startTime: '13:45', endTime: '13:50', quota: 2 },
    { id: '5', startTime: '14:15', endTime: '14:20', quota: 2 },
    { id: '6', startTime: '14:45', endTime: '14:50', quota: 2 },
    { id: '7', startTime: '15:15', endTime: '15:20', quota: 2 },
  ]);

  const weekDays = [
    { key: 'monday', label: '星期一', shortLabel: '周一' },
    { key: 'tuesday', label: '星期二', shortLabel: '周二' },
    { key: 'wednesday', label: '星期三', shortLabel: '周三' },
    { key: 'thursday', label: '星期四', shortLabel: '周四' },
    { key: 'friday', label: '星期五', shortLabel: '周五' },
    { key: 'saturday', label: '星期六', shortLabel: '周六' },
    { key: 'sunday', label: '星期日', shortLabel: '周日' },
  ];

  // 切换星期开关
  const handleWeekToggle = (dayKey: string, enabled: boolean) => {
    setWeekSchedule(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey],
        enabled,
      },
    }));
  };

  // 添加时段
  const handleAddTimeSlot = () => {
    const newSlot: TimeSlot = {
      id: Date.now().toString(),
      startTime: '09:00',
      endTime: '09:05',
      quota: 2,
    };
    setTimeSlots(prev => [...prev, newSlot]);
  };

  // 删除时段
  const handleDeleteTimeSlot = (id: string) => {
    setTimeSlots(prev => prev.filter(slot => slot.id !== id));
  };

  // 更新时段
  const handleUpdateTimeSlot = (id: string, field: keyof TimeSlot, value: any) => {
    setTimeSlots(prev =>
      prev.map(slot =>
        slot.id === id ? { ...slot, [field]: value } : slot
      )
    );
  };

  // 快捷添加时段
  const handleQuickAdd = () => {
    const commonSlots = [
      { startTime: '08:00', endTime: '08:05', quota: 2 },
      { startTime: '10:00', endTime: '10:05', quota: 2 },
      { startTime: '16:00', endTime: '16:05', quota: 2 },
    ];

    const newSlots = commonSlots.map(slot => ({
      ...slot,
      id: Date.now().toString() + Math.random(),
    }));

    setTimeSlots(prev => [...prev, ...newSlots]);
    Message.success('已快速添加常用时段');
  };

  // 保存设置
  const handleSave = () => {
    // 这里可以调用API保存设置
    console.log('保存设置:', { weekSchedule, timeSlots });
    Message.success('设置已保存');
  };

  // 取消设置
  const handleCancel = () => {
    // 重置到初始状态或从服务器重新获取
    Message.info('已取消修改');
  };

  return (
    <div className={styles.container}>
      <Row gutter={24}>
        {/* 左侧：设置每周可约时间 */}
        <Col span={10}>
          <Card className={styles.leftCard}>
            <Title heading={5} className={styles.cardTitle}>
              设置每周可约时间
            </Title>

            <div className={styles.weekSettings}>
              <Row className={styles.headerRow}>
                <Col span={8}>
                  <Text className={styles.headerText}>星期</Text>
                </Col>
                <Col span={16}>
                  <Text className={styles.headerText}>时段表</Text>
                </Col>
              </Row>

              {weekDays.map(day => (
                <Row key={day.key} className={styles.weekRow} align="center">
                  <Col span={8} className={styles.weekCol}>
                    <Space>
                      <Switch
                        size="default"
                        checked={weekSchedule[day.key]?.enabled}
                        onChange={(enabled) => handleWeekToggle(day.key, enabled)}
                      />
                      <Text>{day.label}</Text>
                    </Space>
                  </Col>
                  <Col span={16} className={styles.slotsCol}>
                    <Space align="center">
                      <Text>{day.shortLabel}({weekSchedule[day.key]?.slots || 0})</Text>
                      <Button
                        type="text"
                        size="small"
                        icon={<IconPlus />}
                        className={styles.expandBtn}
                      />
                    </Space>
                  </Col>
                </Row>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧：付费预约 */}
        <Col span={14}>
          <Card className={styles.rightCard}>
            <Title heading={5} className={styles.cardTitle}>
              付费预约
            </Title>

            <div className={styles.timeSlotSettings}>
              <Row className={styles.headerRow}>
                <Col span={8}>
                  <Text className={styles.headerText}>时段</Text>
                </Col>
                <Col span={8}>
                  <Text className={styles.headerText}>名额</Text>
                </Col>
                <Col span={8}></Col>
              </Row>

              {timeSlots.map(slot => (
                <Row key={slot.id} className={styles.timeSlotRow} align="middle">
                  <Col span={8}>
                    <Space>
                      <TimePicker
                        format="HH:mm"
                        value={slot.startTime}
                        onChange={(timeString) =>
                          handleUpdateTimeSlot(slot.id, 'startTime', timeString)
                        }
                        size="small"
                        style={{ width: 70 }}
                      />
                      <TimePicker
                        format="HH:mm"
                        value={slot.endTime}
                        onChange={(timeString) =>
                          handleUpdateTimeSlot(slot.id, 'endTime', timeString)
                        }
                        size="small"
                        style={{ width: 70 }}
                      />
                    </Space>
                  </Col>
                  <Col span={8}>
                    <InputNumber
                      value={slot.quota}
                      onChange={(value) =>
                        handleUpdateTimeSlot(slot.id, 'quota', value)
                      }
                      min={1}
                      max={99}
                      size="small"
                      style={{ width: 60 }}
                    />
                  </Col>
                  <Col span={8} className={styles.actionCol}>
                    <Button
                      type="text"
                      size="small"
                      icon={<IconClose />}
                      onClick={() => handleDeleteTimeSlot(slot.id)}
                      className={styles.deleteBtn}
                    />
                  </Col>
                </Row>
              ))}

              <div className={styles.addActions}>
                <Space>
                  <Button
                    type="outline"
                    size="small"
                    icon={<IconPlus />}
                    onClick={handleAddTimeSlot}
                  >
                    添加时段
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    icon={<IconThunderbolt />}
                    onClick={handleQuickAdd}
                    className={styles.quickAddBtn}
                  >
                    快捷添加
                  </Button>
                </Space>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 底部操作按钮 */}
      <div className={styles.footer}>
        <Space>
          <Button onClick={handleCancel}>
            取消
          </Button>
          <Button type="primary" onClick={handleSave}>
            保存设置
          </Button>
        </Space>
      </div>
    </div>
  );
}
