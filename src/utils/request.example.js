// request.js 使用示例

import api, { setTokens, clearTokens, getAccessToken } from './request';

// 1. 登录示例
export const login = async (username, password) => {
  try {
    const response = await api.post('/auth/login', {
      username,
      password,
    });

    const { accessToken, refreshToken } = response.data.data;

    // 保存token
    setTokens(accessToken, refreshToken);

    return response.data;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};

// 2. 退出登录示例
export const logout = async () => {
  try {
    await api.post('/auth/logout');
  } catch (error) {
    console.error('Logout failed:', error);
  } finally {
    // 无论成功失败都清除本地token
    clearTokens();
    window.location.hash = '/login';
  }
};

// 3. 获取用户信息示例（需要token）
export const getUserInfo = async () => {
  try {
    const response = await api.get('/user/info');
    return response.data;
  } catch (error) {
    console.error('Get user info failed:', error);
    throw error;
  }
};

// 4. 更新用户信息示例
export const updateUserInfo = async (userInfo) => {
  try {
    const response = await api.put('/user/info', userInfo);
    return response.data;
  } catch (error) {
    console.error('Update user info failed:', error);
    throw error;
  }
};

// 5. 获取列表数据示例（带分页参数）
export const getDataList = async (page = 1, pageSize = 10, filters = {}) => {
  try {
    const response = await api.get('/data/list', {
      page,
      pageSize,
      ...filters,
    });
    return response.data;
  } catch (error) {
    console.error('Get data list failed:', error);
    throw error;
  }
};

// 6. 文件上传示例
export const uploadFile = async (file, onProgress) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.upload('/upload', formData, {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        onProgress && onProgress(progress);
      },
    });

    return response.data;
  } catch (error) {
    console.error('Upload file failed:', error);
    throw error;
  }
};

// 7. 文件下载示例
export const downloadFile = async (fileId, filename) => {
  try {
    const response = await api.download(`/download/${fileId}`);

    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download file failed:', error);
    throw error;
  }
};

// 8. 批量删除示例
export const batchDelete = async (ids) => {
  try {
    const response = await api.delete('/data/batch', {
      data: { ids },
    });
    return response.data;
  } catch (error) {
    console.error('Batch delete failed:', error);
    throw error;
  }
};

// 9. 检查token是否存在
export const isAuthenticated = () => {
  return !!getAccessToken();
};

// 10. 在React组件中使用的Hook示例
import { useState, useEffect } from 'react';

export const useUserInfo = () => {
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setLoading(true);
        const data = await getUserInfo();
        setUserInfo(data);
        setError(null);
      } catch (err) {
        setError(err);
        setUserInfo(null);
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated()) {
      fetchUserInfo();
    } else {
      setLoading(false);
    }
  }, []);

  return { userInfo, loading, error };
};
