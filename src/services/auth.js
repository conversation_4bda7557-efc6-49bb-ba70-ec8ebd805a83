// 认证相关的API服务
import api, { setTokens, clearTokens } from '@/utils/request';

/**
 * 用户登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise} 登录结果
 */
export const login = async (username, password) => {
  try {
    const response = await api.post('/user/login', {
      username: username,
      password: password,
    });

    const { code, msg, data } = response.data;

    if (code === 0) {
      // 如果后端返回了token，保存到本地
      if (data && data.access_token) {
        setTokens(data.access_token, data.refresh_token);
      }

      return { success: true, data };
    } else {
      return { success: false, message: msg };
    }
  } catch (error) {
    console.error('Login failed:', error);
    return {
      success: false,
      message: error.response?.data?.msg || '登录失败，请稍后重试',
    };
  }
};

/**
 * 用户退出登录
 * @returns {Promise} 退出结果
 */
export const logout = async () => {
  try {
    // 调用后端退出接口
    await api.post('/user/logout');
  } catch (error) {
    console.error('Logout API failed:', error);
    // 即使后端接口失败，也要清除本地数据
  } finally {
    // 清除本地存储的token和状态
    clearTokens();
    // 跳转到登录页
    window.location.hash = '/login';
  }
};

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
export const getUserInfo = async () => {
  try {
    const response = await api.get('/user/info');
    return response.data;
  } catch (error) {
    console.error('Get user info failed:', error);
    throw error;
  }
};

/**
 * 修改密码
 * @param {string} oldPassword 旧密码
 * @param {string} newPassword 新密码
 * @returns {Promise} 修改结果
 */
export const changePassword = async (oldPassword, newPassword) => {
  try {
    const response = await api.post('/user/change-password', {
      oldPassword,
      newPassword,
    });

    return response.data;
  } catch (error) {
    console.error('Change password failed:', error);
    throw error;
  }
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isAuthenticated = () => {
  const userStatus = localStorage.getItem('userStatus');
  return userStatus === 'login';
};

/**
 * 刷新用户token（手动调用）
 * @returns {Promise} 刷新结果
 */
export const refreshUserToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await api.post('/auth/refresh', {
      refreshToken: refreshToken,
    });

    const { accessToken, refreshToken: newRefreshToken } = response.data.data;
    setTokens(accessToken, newRefreshToken);

    return { success: true, accessToken };
  } catch (error) {
    console.error('Refresh token failed:', error);
    clearTokens();
    return { success: false, error };
  }
};
