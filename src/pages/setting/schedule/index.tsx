import { useState } from 'react'
import { Card, Switch, Button, TimePicker, InputNumber, Space, Typography, Grid, Message, Radio, DatePicker } from '@arco-design/web-react'
import { IconPlus, IconClose, IconThunderbolt, IconRight } from '@arco-design/web-react/icon'
import styles from './index.module.less'

const { Title, Text } = Typography
const { Row, Col } = Grid

interface TimeSlot {
  id: string
  startTime: string
  endTime: string
  quota: number
}

interface WeekSchedule {
  [key: string]: {
    enabled: boolean
    timeSlots: TimeSlot[]
  }
}

export default function ScheduleSettings() {
  // 当前选中的星期
  const [selectedDay, setSelectedDay] = useState<string>('monday')

  // 日期范围设置状态
  const [dateRangeType, setDateRangeType] = useState<string>('fixed') // 'permanent', 'future', 'fixed'
  const [futureDays, setFutureDays] = useState<number>(30)
  const [dateRange, setDateRange] = useState<[string, string]>(['2025-08-25', '2025-09-21'])

  // 星期设置状态
  const [weekSchedule, setWeekSchedule] = useState<WeekSchedule>({
    monday: {
      enabled: true,
      timeSlots: [
        { id: '1', startTime: '08:45', endTime: '08:50', quota: 2 },
        { id: '2', startTime: '09:15', endTime: '09:20', quota: 2 },
        { id: '3', startTime: '09:45', endTime: '09:50', quota: 2 },
        { id: '4', startTime: '13:45', endTime: '13:50', quota: 2 },
        { id: '5', startTime: '14:15', endTime: '14:20', quota: 2 },
        { id: '6', startTime: '14:45', endTime: '14:50', quota: 2 },
        { id: '7', startTime: '15:15', endTime: '15:20', quota: 2 },
      ]
    },
    tuesday: { enabled: true, timeSlots: [] },
    wednesday: { enabled: true, timeSlots: [] },
    thursday: { enabled: true, timeSlots: [] },
    friday: { enabled: true, timeSlots: [] },
    saturday: { enabled: true, timeSlots: [] },
    sunday: { enabled: true, timeSlots: [] },
  })

  const weekDays = [
    { key: 'monday', label: '星期一', shortLabel: '周一' },
    { key: 'tuesday', label: '星期二', shortLabel: '周二' },
    { key: 'wednesday', label: '星期三', shortLabel: '周三' },
    { key: 'thursday', label: '星期四', shortLabel: '周四' },
    { key: 'friday', label: '星期五', shortLabel: '周五' },
    { key: 'saturday', label: '星期六', shortLabel: '周六' },
    { key: 'sunday', label: '星期日', shortLabel: '周日' },
  ]

  // 获取当前选中星期的时段
  const currentTimeSlots = weekSchedule[selectedDay]?.timeSlots || []

  // 切换星期开关
  const handleWeekToggle = (dayKey: string, enabled: boolean) => {
    setWeekSchedule(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey],
        enabled,
      },
    }))
  }

  // 选择星期
  const handleDaySelect = (dayKey: string) => {
    setSelectedDay(dayKey)
  }

  // 添加时段
  const handleAddTimeSlot = () => {
    const newSlot: TimeSlot = {
      id: Date.now().toString(),
      startTime: '',
      endTime: '',
      quota: 1,
    }
    setWeekSchedule(prev => ({
      ...prev,
      [selectedDay]: {
        ...prev[selectedDay],
        timeSlots: [...prev[selectedDay].timeSlots, newSlot],
      },
    }))
  }

  // 删除时段
  const handleDeleteTimeSlot = (id: string) => {
    setWeekSchedule(prev => ({
      ...prev,
      [selectedDay]: {
        ...prev[selectedDay],
        timeSlots: prev[selectedDay].timeSlots.filter(slot => slot.id !== id),
      },
    }))
  }

  // 更新时段
  const handleUpdateTimeSlot = (id: string, field: keyof TimeSlot, value: any) => {
    setWeekSchedule(prev => ({
      ...prev,
      [selectedDay]: {
        ...prev[selectedDay],
        timeSlots: prev[selectedDay].timeSlots.map(slot =>
          slot.id === id ? { ...slot, [field]: value } : slot
        ),
      },
    }))
  }

  // 快捷添加时段
  const handleQuickAdd = () => {
    const commonSlots = [
      { startTime: '08:00', endTime: '08:05', quota: 2 },
      { startTime: '10:00', endTime: '10:05', quota: 2 },
      { startTime: '16:00', endTime: '16:05', quota: 2 },
    ]

    const newSlots = commonSlots.map(slot => ({
      ...slot,
      id: Date.now().toString() + Math.random(),
    }))

    setWeekSchedule(prev => ({
      ...prev,
      [selectedDay]: {
        ...prev[selectedDay],
        timeSlots: [...prev[selectedDay].timeSlots, ...newSlots],
      },
    }))
    Message.success('已快速添加常用时段')
  }

  // 保存设置
  const handleSave = () => {
    // 这里可以调用API保存设置
    console.log('保存设置:', { weekSchedule })
    Message.success('设置已保存')
  }

  return (
    <Card className={styles.container}>
      <div className={styles.dateRangeSettings}>
        <Title heading={6} className={styles.sectionTitle}>
          设置可约日期范围
        </Title>
        <div className={styles.radioGroup}>
          <Radio.Group
            value={dateRangeType}
            onChange={setDateRangeType}
            direction="vertical"
          >
            <Radio value="permanent">
              <Text style={{ marginLeft: 6 }}>永久可约</Text>
            </Radio>

            <Radio value="future">
              <Text style={{ marginLeft: 6 }}>预约未来</Text>&nbsp;&nbsp;&nbsp;
              <InputNumber
                value={futureDays}
                onChange={setFutureDays}
                min={1}
                max={365}
                size="small"
                style={{ width: 80 }}
                disabled={dateRangeType !== 'future'}
              />&nbsp;&nbsp;&nbsp;
              <Text>天内的项目</Text>
            </Radio>

            <Radio value="fixed">
              <Text style={{ marginLeft: 6 }}>预约固定日期范围内的项目</Text>
            </Radio>
          </Radio.Group>

          {dateRangeType === 'fixed' && (
            <div className={styles.dateRangePicker}>
              <DatePicker.RangePicker
                value={dateRange}
                onChange={(dateString) => setDateRange(dateString as [string, string])}
                style={{ width: '100%', maxWidth: 300 }}
                format="YYYY-MM-DD"
              />
            </div>
          )}
        </div>
      </div>
      <Row gutter={16}>
        {/* 左侧：设置每周可约时间 */}
        <Col span={8}>
          <Card bordered={true} title="设置每周可约时间">
            {/* 日期范围设置 */}
            <div className={styles.weekSettings}>
              <Row className={styles.headerRow}>
                <Col span={10}>
                  <Text className={styles.headerText}>星期</Text>
                </Col>
                <Col span={14}>
                  <Text className={styles.headerText}>时段表</Text>
                </Col>
              </Row>

              {weekDays.map(day => (
                <Row
                  key={day.key}
                  className={`${styles.weekRow} ${selectedDay === day.key ? styles.selectedWeekRow : ''}`}
                  align="center"
                  onClick={() => handleDaySelect(day.key)}
                >
                  <Col span={10} className={styles.weekCol}>
                    <Space>
                      <Switch
                        size="small"
                        checked={weekSchedule[day.key]?.enabled}
                        onChange={(enabled) => handleWeekToggle(day.key, enabled)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <Text>{day.label}</Text>
                    </Space>
                  </Col>
                  <Col span={13} className={styles.slotsCol}>
                    <Text>{day.shortLabel}({weekSchedule[day.key]?.timeSlots?.length || 0})</Text>
                  </Col>
                  {selectedDay === day.key && (
                    <Col span={1}>
                      <IconRight />
                    </Col>
                  )}
                </Row>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧：付费预约 */}
        <Col span={16}>
          <Card bordered={true} title={weekDays.find(d => d.key === selectedDay)?.label} className={styles.timeSlotCard}>
            <div className={styles.timeSlotSettings}>
              <div className={styles.headerRow}>
                <Text className={styles.headerText} style={{ width: '210px' }}>时段</Text>
                <Text className={styles.headerText}>名额</Text>
              </div>
              <Space direction='vertical'>
                {currentTimeSlots.map((slot: TimeSlot) => (
                  <Space key={slot.id} className={styles.timeSlotRow}>
                    <TimePicker.RangePicker
                      format="HH:mm"
                      value={[slot.startTime, slot.endTime]}
                      onChange={(timeString) =>
                        handleUpdateTimeSlot(slot.id, 'startTime', timeString)
                      }
                      style={{ width: 200 }}
                    />
                    <InputNumber
                      value={slot.quota}
                      onChange={(value) =>
                        handleUpdateTimeSlot(slot.id, 'quota', value)
                      }
                      min={1}
                      max={99}
                      style={{ width: 100 }}
                    />
                    <Button
                      type="text"
                      icon={<IconClose />}
                      onClick={() => handleDeleteTimeSlot(slot.id)}
                      className={styles.deleteBtn}
                    />
                  </Space>
                ))}
              </Space>
              <div className={styles.addActions}>
                <Space>
                  <Button
                    type="outline"
                    size="small"
                    icon={<IconPlus />}
                    onClick={handleAddTimeSlot}
                  >
                    添加时段
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    icon={<IconThunderbolt />}
                    onClick={handleQuickAdd}
                    className={styles.quickAddBtn}
                  >
                    快捷添加
                  </Button>
                </Space>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 底部操作按钮 */}
      <div className={styles.footer}>
        <Space>
          <Button type="primary" onClick={handleSave}>
            保存设置
          </Button>
        </Space>
      </div>
    </Card>
  )
}
