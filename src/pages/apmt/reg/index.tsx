import { useEffect, useMemo, useState } from 'react'
import { Card, Table, Button, Space, Input, DatePicker, Tag, Link, Popconfirm, Message, Radio, Modal, Form, Select, Divider } from '@arco-design/web-react'
import { IconSearch, IconPlus, IconUser, IconCalendar, IconMobile, IconFile, IconDelete } from '@arco-design/web-react/icon'
import request from '@/utils/request'
import './index.css'

const { RangePicker } = DatePicker

const getTypeTag = (type: string) => {
  const typeMap = {
    new: { text: '新客', color: 'blue' },
    old: { text: '老客', color: 'orangered' },
  }
  const config = typeMap[type] || { text: type, color: 'blue' }
  return <Tag color={config.color}>{config.text}</Tag>
}

export default function Index() {
  const [type, setType] = useState<string>('all')
  const [addForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState([])
  const [brandData, setBrandData] = useState(null)
  const [keyword, setKeyword] = useState('')
  const [dateRange, setDateRange] = useState([])
  const [addModalVisible, setAddModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectedRow, setSelectedRow] = useState(null)
  const [brandInputValue, setBrandInputValue] = useState('')
  const [pagination, setPagination] = useState({
    total: 0,
    pageSize: 10,
    current: 1,
  })

  const addFormType = Form.useWatch('type', addForm)
  const addFormBrand = Form.useWatch('brand', addForm)
  const addBrandOptions = useMemo(() => {
    if (!brandData || !addFormType || !brandData[addFormType]) return []
    return brandData[addFormType].map(item => ({ label: item.name, value: item.name }))
  }, [addFormType])
  const addBreedOptions = useMemo(() => {
    if (!brandData || !addFormBrand || !brandData[addFormType]) return []
    return brandData[addFormType].find(item => item.name === addFormBrand)?.children.map(item => ({ label: item.name, value: item.name })) || []
  }, [addFormBrand])

  const editFormType = Form.useWatch('type', editForm)
  const editFormBrand = Form.useWatch('brand', editForm)
  const editBrandOptions = useMemo(() => {
    if (!brandData || !editFormType || !brandData[editFormType]) return []
    return brandData[editFormType].map(item => ({ label: item.name, value: item.name }))
  }, [editFormType])
  const editBreedOptions = useMemo(() => {
    if (!brandData || !editFormBrand || !brandData[editFormType]) return []
    return brandData[editFormType].find(item => item.name === editFormBrand)?.children.map(item => ({ label: item.name, value: item.name })) || []
  }, [editFormBrand])
  console.log('brandOptions', addBrandOptions)

  useEffect(() => {
    fetchData()
  }, [pagination.current, type])

  useEffect(() => {
    request.get('/brand/listMap').then(({ data: result }) => {
      if (result.code == 0) {
        setBrandData(result.data)
      }
    })
  }, [])

  useEffect(() => {
    if (addFormType) {
      addForm.setFieldsValue({ 'brand': '', 'breed': '' })
    }
  }, [addFormType])

  // 监听brand变化，重置breed
  useEffect(() => {
    if (addFormBrand) {
      addForm.setFieldValue('breed', '')
    }
  }, [addFormBrand])

  const fetchData = () => {
    setLoading(true)
    request
      .get('/apmt/regList', {
        keyword,
        start_date: dateRange[0],
        end_date: dateRange[1],
        ...pagination,
        type
      })
      .then((res) => {
        setData(res.data.data)
        setPagination({
          ...pagination,
          total: res.data.total,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const addBrandItem = () => {
    if (!brandInputValue) return
    addForm.setFieldValue('brand', brandInputValue)
    setBrandInputValue('')
  }

  const columns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        return getTypeTag(type)
      }
    },
    {
      title: '登记日期',
      dataIndex: 'reg_date',
      key: 'reg_date',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '档案编号',
      dataIndex: 'file_no',
      key: 'file_no',
    },
    {
      title: '提交时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: (_, record) => (
        <Space key="action">
          <Link key="edit" onClick={_ => {
            setEditModalVisible(true)
            editForm.setFieldsValue(record)
            setSelectedRow(record)
          }}>
            编辑
          </Link>
          <Popconfirm
            focusLock
            title='提示'
            content='确定要重试吗？'
            onOk={() => {
              request.post('/apmt/delReg', { id: record.id }).then(({ data: result }) => {
                if (result.code != 0) {
                  Message.error(result.msg)
                  return
                }
                Message.success('删除成功')
                fetchData()
              })
            }}
          >
            <Link key="delete">
              删除
            </Link>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Radio.Group
              type='button'
              name='type'
              value={type}
              onChange={setType}
            >
              <Radio value='all'>全部</Radio>
              <Radio value='new'>新客</Radio>
              <Radio value='old'>老客</Radio>
            </Radio.Group>
            <Input
              placeholder="姓名电话或档案编号"
              prefix={<IconSearch />}
              onChange={(value) => setKeyword(value)}
              onPressEnter={() => {
                fetchData()
              }}
              allowClear
              style={{ width: 200 }}
            />
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={(value) => setDateRange(value)}
            />
            <Button
              type="primary"
              icon={<IconSearch />}
              onClick={() => {
                fetchData()
              }}
            >
              查询
            </Button>
            <Button
              icon={<IconPlus />}
              onClick={() => {
                addForm.resetFields()
                setAddModalVisible(true)
              }}
            >
              新增登记
            </Button>
          </Space>
        </div>
        <Table
          rowKey="id"
          columns={columns}
          loading={loading}
          data={data}
          pagination={pagination}
          onChange={(pagi) => {
            setPagination({
              ...pagination,
              current: pagi.current,
            })
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRowKeys(selectedRowKeys)
            },
          }}
          renderPagination={(paginationNode) => (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: 10,
              }}
            >
              <Space>
                {selectedRowKeys.length > 0 && (
                  <>
                    <Button icon={<IconMobile />}>
                      发短信
                    </Button>
                    <Popconfirm
                      focusLock
                      title='提示'
                      content={`确定要删除已选中 ${selectedRowKeys.length} 项吗？`}
                      onOk={() => {
                        return new Promise<void>((resolve, reject) => {
                          request.post('/apmt/delReg', { id: selectedRowKeys.join(',') }).then(({ data: result }) => {
                            if (result.code != 0) {
                              Message.error(result.msg)
                              reject()
                              return
                            }
                            Message.success('删除成功')
                            fetchData()
                            resolve()
                          })
                        })
                      }}
                      onCancel={() => {
                        Message.error({
                          content: 'cancel',
                        });
                      }}
                    >
                      <Button icon={<IconDelete />}>
                        批量删除
                      </Button>
                    </Popconfirm>

                  </>
                )}
              </Space>
              {paginationNode}
            </div>
          )}
        />
      </Card>
      {/* 新增 */}
      <Modal
        key="create"
        title="新增登记"
        visible={addModalVisible}
        unmountOnExit
        onCancel={() => {
          setAddModalVisible(false);
        }}
        onOk={() => {
          addForm.validate().then((data) => {
            request.post('/apmt/addReg', data).then(({ data: result }) => {
              if (result.code != 0) {
                Message.error(result.msg)
                return
              }
              setAddModalVisible(false)
              fetchData()
            })
          }).catch(e => {
            console.log(e.errors)
          })
        }}
      >
        <Form form={addForm} autoComplete='off'>
          <Form.Item
            label="类型"
            field="type"
            initialValue="new"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Radio.Group
              type='button'
            >
              <Radio value='new'>新客</Radio>
              <Radio value='old'>老客</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='登记日期'
            field="reg_date"
            rules={[{ required: true, message: '请选择登记日期' }]}
          >
            <DatePicker prefix={<IconCalendar />} />
          </Form.Item>
          <Form.Item label='镜片品牌' style={{ marginBottom: 0 }}>
            <Space>
              <Form.Item field="brand">
                <Select
                  placeholder='请选择品牌'
                  style={{ width: 154 }}
                  options={addBrandOptions}
                // dropdownRender={(menu) => (
                //   <div>
                //     {menu}
                //     <Divider style={{ margin: 0 }} />
                //     <div
                //       style={{
                //         display: 'flex',
                //         alignItems: 'center',
                //         padding: '10px 12px',
                //       }}
                //     >
                //       <Input
                //         size='small'
                //         style={{ marginRight: 6 }}
                //         value={brandInputValue}
                //         onChange={(value) => setBrandInputValue(value)}
                //       />
                //       <Button
                //         style={{ fontSize: 14, padding: '0 6px' }}
                //         type='text'
                //         size='mini'
                //         onClick={addBrandItem}
                //       >
                //         <IconPlus />
                //       </Button>
                //     </div>
                //   </div>
                // )}
                />
              </Form.Item>
              <Form.Item field="breed">
                <Select
                  placeholder='请选择品牌'
                  style={{ width: 154 }}
                  options={addBreedOptions}
                />
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item
            label='姓名'
            field="name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input prefix={<IconUser />} placeholder='请输入姓名' />
          </Form.Item>
          <Form.Item
            label='联系方式'
            field="phone"
            rules={[{ required: true, message: '请输入联系方式' }]}
          >
            <Input prefix={<IconMobile />} placeholder='请输入联系方式' />
          </Form.Item>
          <Form.Item
            label='档案编号'
            field="file_no"
            rules={[{ required: true, message: '请输入档案编号' }]}
          >
            <Input prefix={<IconFile />} placeholder='请输入档案编号' />
          </Form.Item>
        </Form>
      </Modal>
      {/* 编辑 */}
      <Modal
        key="update"
        title="编辑登记"
        visible={editModalVisible}
        unmountOnExit
        onCancel={() => {
          setEditModalVisible(false)
        }}
        onOk={() => {
          return new Promise<void>((resolve, reject) => {
            editForm.validate().then((data) => {
              data.id = selectedRow.id
              request.post('/apmt/editReg', data).then(({ data: result }) => {
                if (result.code != 0) {
                  Message.error(result.msg)
                  reject()
                  return
                }
                setEditModalVisible(false)
                fetchData()
                resolve()
              })
            }).catch(e => {
              console.log(e.errors)
              reject()
            })
          })
        }}
      >
        <Form form={editForm} autoComplete='off'>
          <Form.Item
            label="类型"
            field="type"
            initialValue="new"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Radio.Group
              type='button'
            >
              <Radio value='new'>新客</Radio>
              <Radio value='old'>老客</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='登记日期'
            field="reg_date"
            rules={[{ required: true, message: '请选择登记日期' }]}
          >
            <DatePicker prefix={<IconCalendar />} />
          </Form.Item>
          <Form.Item label='镜片品牌' style={{ marginBottom: 0 }}>
            <Space>
              <Form.Item field="brand">
                <Select
                  placeholder='请选择品牌'
                  style={{ width: 154 }}
                  options={editBrandOptions}
                />
              </Form.Item>
              <Form.Item field="breed">
                <Select
                  placeholder='请选择品牌'
                  style={{ width: 154 }}
                  options={editBreedOptions}
                />
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item
            label='姓名'
            field="name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input prefix={<IconUser />} placeholder='请输入姓名' />
          </Form.Item>
          <Form.Item
            label='联系方式'
            field="phone"
            rules={[{ required: true, message: '请输入联系方式' }]}
          >
            <Input prefix={<IconMobile />} placeholder='请输入联系方式' />
          </Form.Item>
          <Form.Item
            label='档案编号'
            field="file_no"
            rules={[{ required: true, message: '请输入档案编号' }]}
          >
            <Input prefix={<IconFile />} placeholder='请输入档案编号' />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
