.timeSlotCard {
  width: 400px;
}
.weekSettings,
.timeSlotSettings {
  .headerRow {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--color-border-2);
    
    .headerText {
      font-weight: 500;
      color: var(--color-text-2);
      font-size: 13px;
    }
  }
}

.weekRow {
  margin-bottom: 8px;
  padding: 10px 12px;
  border-radius: 3px;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid #f0f0f0;

  &:hover {
    background-color: #f5f5f5;
    border-color: #f5f5f5;
  }

  &.selectedWeekRow {
    background-color: #f5f5f5;
    border-color: #f5f5f5;

    &:hover {
      background-color: #f5f5f5;
      border-color: #f5f5f5;
    }
  }

  .weekCol {
    display: flex;
    align-items: center;
  }

  .slotsCol {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}

.timeSlotRow {
  margin-bottom: 8px;
  background-color: var(--color-bg-2);
  border-radius: 6px;
  transition: all 0.2s;
  
  .actionCol {
    display: flex;
    justify-content: flex-end;
    
    .deleteBtn {
      color: var(--color-text-3);
      
      &:hover {
        color: var(--color-danger-6);
        background-color: var(--color-danger-1);
      }
    }
  }
}

.addActions {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed var(--color-border-2);
  
  .quickAddBtn {
    color: var(--color-warning-6);
    
    &:hover {
      color: var(--color-warning-7);
      background-color: var(--color-warning-1);
    }
  }
}

.footer {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}

// 暗色主题下的选中状态
body[arco-theme='dark'] {
  .weekRow.selectedWeekRow {
    background-color: var(--color-primary-dark-1);
    border-color: var(--color-primary-6);

    &:hover {
      background-color: var(--color-primary-dark-2);
    }
  }
}

// 暗色主题适配
body[arco-theme='dark'] {
  .leftCard,
  .rightCard {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .timeSlotRow {
    &:hover {
      box-shadow: 0 1px 4px rgba(255, 255, 255, 0.1);
    }
  }
}
