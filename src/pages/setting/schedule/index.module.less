.container {
  padding: 20px;
  background-color: var(--color-bg-1);
  min-height: calc(100vh - 120px);
}

.leftCard,
.rightCard {
  height: fit-content;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cardTitle {
  margin-bottom: 20px !important;
  font-weight: 600;
  color: var(--color-text-1);
}

.weekSettings,
.timeSlotSettings {
  .headerRow {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--color-border-2);
    
    .headerText {
      font-weight: 500;
      color: var(--color-text-2);
      font-size: 13px;
    }
  }
}

.weekRow {
  margin-bottom: 12px;
  padding: 8px 0;
  border-radius: 6px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: var(--color-bg-2);
  }
  
  .weekCol {
    display: flex;
    align-items: center;
  }
  
  .slotsCol {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .expandBtn {
      color: var(--color-text-3);
      
      &:hover {
        color: var(--color-primary-6);
      }
    }
  }
}

.timeSlotRow {
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: var(--color-bg-2);
  border-radius: 6px;
  transition: all 0.2s;
  
  &:hover {
    background-color: var(--color-bg-3);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }
  
  .actionCol {
    display: flex;
    justify-content: flex-end;
    
    .deleteBtn {
      color: var(--color-text-3);
      
      &:hover {
        color: var(--color-danger-6);
        background-color: var(--color-danger-1);
      }
    }
  }
}

.addActions {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed var(--color-border-2);
  
  .quickAddBtn {
    color: var(--color-warning-6);
    
    &:hover {
      color: var(--color-warning-7);
      background-color: var(--color-warning-1);
    }
  }
}

.footer {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}

// 响应式设计
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .weekRow,
  .timeSlotRow {
    padding: 6px 8px;
  }
  
  .footer {
    margin-top: 24px;
    justify-content: center;
  }
}

// 暗色主题适配
body[arco-theme='dark'] {
  .leftCard,
  .rightCard {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .timeSlotRow {
    &:hover {
      box-shadow: 0 1px 4px rgba(255, 255, 255, 0.1);
    }
  }
}
