import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Avatar,
  Tag,
  Modal,
  Form,
  Message,
  Link,
  Popconfirm,
} from '@arco-design/web-react';
import { IconSearch, IconPlus } from '@arco-design/web-react/icon';
import request from '@/utils/request';
import { useSelector } from 'react-redux';

const FormItem = Form.Item;

const getStateTag = (value) => {
  const statusMap = {
    new: { text: '正常', color: 'green' },
    disable: { text: '禁用', color: 'red' },
  };
  const config = statusMap[value] || { text: value, color: 'gray' };
  return <Tag color={config.color}>{config.text}</Tag>;
};

export default function UserList() {
  const [visible, setVisible] = useState(false);
  const [resetVisible, setResetVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();
  const [resetForm] = Form.useForm();
  const [data, setData] = useState([]);
  const userInfo = useSelector((state: any) => state.userInfo || {});
  const [keyword, setKeyword] = useState('');
  const [role, setRole] = useState('');
  const [state, setState] = useState('');
  const [pagination, setPagination] = useState({
    total: 0,
    pageSize: 10,
    current: 1,
  });

  const fetchData = () => {
    request
      .get('/user/list', { keyword, role, state, ...pagination })
      .then((res) => {
        setData(res.data.data);
        setPagination({
          ...pagination,
          total: res.data.total,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current]);

  const columns = [
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      render: (avatar, record) => (
        <Avatar size={40}>
          {avatar ? <img src={avatar} alt="avatar" /> : record.name?.charAt(0)}
        </Avatar>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色',
      dataIndex: 'is_super',
      key: 'is_super',
      render: (isSuper) => {
        const roleMap = {
          1: { text: '管理员', color: 'orange' },
          0: { text: '普通', color: 'blue' },
        };
        const config = roleMap[isSuper] || { text: isSuper, color: 'gray' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: (value) => {
        return getStateTag(value);
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 190,
      render: (_, record) => (
        <Space>
          <Link
            disabled={userInfo.is_super === 0}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Link>
          <Link
            disabled={userInfo.is_super === 0}
            onClick={() => handleReset(record)}
          >
            重置密码
          </Link>
          <Popconfirm
            focusLock
            title="确认删除"
            content={`确定要删除用户 ${record.name} 吗？`}
            onOk={() => {
              handleDelete(record);
            }}
          >
            <Link disabled={userInfo.is_super === 0}>
              删除
            </Link>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingUser(null);
    form.resetFields();
    setVisible(true);
  };

  const handleEdit = (record) => {
    setEditingUser(record);
    form.setFieldsValue(record);
    setVisible(true);
  };

  const handleReset = (record) => {
    setEditingUser(record);
    resetForm.resetFields();
    setResetVisible(true);
  };

  const handleDelete = (record) => {
    request.post(`/user/delete`, { id: record.id }).then((res) => {
      if (res.data.code === 0) {
        fetchData();
        Message.success('删除成功');
      } else {
        Message.error(res.data.msg);
      }
    });
  };

  const handleSubmit = () => {
    form
      .validate()
      .then((values) => {
        let url = '';
        if (editingUser) {
          url = '/user/update';
          values.id = editingUser.id;
        } else {
          url = '/user/create';
        }
        request.post(url, values).then((res) => {
          if (res.data.code === 0) {
            fetchData();
            Message.success(editingUser ? '更新成功' : '创建成功');
          } else {
            Message.error(res.data.msg);
          }
        });

        setVisible(false);
      })
      .catch((error) => {
        console.log('Validation failed:', error);
      });
  };

  const handleResetSubmit = () => {
    resetForm
      .validate()
      .then((values) => {
        values.id = editingUser.id;
        request.post('/user/update', values).then((res) => {
          if (res.data.code === 0) {
            Message.success('重置成功');
          } else {
            Message.error(res.data.msg);
          }
        });

        setResetVisible(false);
      })
      .catch((error) => {
        console.log('Validation failed:', error);
      });
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入用户名或姓名"
              prefix={<IconSearch />}
              allowClear
              style={{ width: 200 }}
              onChange={(value) => setKeyword(value)}
              onPressEnter={() => {
                fetchData();
              }}
            />
            <Select
              placeholder="请选择角色"
              style={{ width: 120 }}
              allowClear
              options={[
                { label: '管理员', value: 'super' },
                { label: '普通', value: 'normal' },
              ]}
              onChange={(value) => setRole(value)}
            />
            <Select
              placeholder="请选择状态"
              style={{ width: 120 }}
              allowClear
              options={[
                { label: '正常', value: 'new' },
                { label: '禁用', value: 'disable' },
              ]}
              onChange={(value) => setState(value)}
            />
            <Button icon={<IconSearch />} onClick={() => fetchData()}>
              查询
            </Button>
            {userInfo.is_super == 1 && (
              <Button type="primary" icon={<IconPlus />} onClick={handleAdd}>
                新增用户
              </Button>
            )}
          </Space>
        </div>
        <Table
          rowKey="id"
          columns={columns}
          data={data}
          pagination={pagination}
          onChange={(pagi) => {
            setPagination({
              ...pagination,
              current: pagi.current,
            });
          }}
        />
      </Card>
      {/* 新增编辑 */}
      <Modal
        title={editingUser ? '编辑用户' : '新增用户'}
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => setVisible(false)}
        autoFocus={false}
        focusLock={true}
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="名称"
            field="name"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input placeholder="请输入名称" />
          </FormItem>
          <FormItem
            label="用户名"
            field="username"
            disabled={!!editingUser}
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" autoComplete="off" />
          </FormItem>
          {!editingUser && (
            <FormItem
              label="密码"
              field="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password placeholder="请输入密码" />
            </FormItem>
          )}
          <FormItem
            label="状态"
            field="state"
            initialValue="new"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select
              placeholder="请选择状态"
              options={[
                { label: '正常', value: 'new' },
                { label: '禁用', value: 'disable' },
              ]}
              renderFormat={(option, value) => {
                return getStateTag(value);
              }}
            />
          </FormItem>
        </Form>
      </Modal>
      {/* 重置密码 */}
      <Modal
        title="重置密码"
        visible={resetVisible}
        onOk={handleResetSubmit}
        onCancel={() => setResetVisible(false)}
        autoFocus={false}
        focusLock={true}
      >
        <Form form={resetForm} layout="vertical">
          <FormItem
            label="密码"
            field="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" />
          </FormItem>
        </Form>
      </Modal>
    </div>
  );
}
