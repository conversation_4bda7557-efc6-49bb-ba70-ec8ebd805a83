import axios from 'axios';

const baseUrl =
  process.env.NODE_ENV === 'development' ? 'http://127.0.0.1:8050' : '';

// 创建axios实例
const request = axios.create({
  baseURL: `${baseUrl}/admin`,
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 存储token的key
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

// 是否正在刷新token的标志
let isRefreshing = false;
// 存储待重试的请求
let failedQueue = [];

// 处理队列中的请求
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// 获取token
const getAccessToken = () => {
  return localStorage.getItem(ACCESS_TOKEN_KEY);
};

const getRefreshToken = () => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

// 设置token
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
  if (refreshToken) {
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
  }
  localStorage.setItem('userStatus', 'login');
};

// 清除token
const clearTokens = () => {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem('userStatus');
};

// 刷新token
const refreshAccessToken = async () => {
  const refreshToken = getRefreshToken();

  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const response = await axios.post('/api/auth/refresh', {
      refreshToken: refreshToken,
    });

    const { access_token, refresh_token: newRefreshToken } = response.data.data;
    setTokens(access_token, newRefreshToken);

    return access_token;
  } catch (error) {
    clearTokens();
    // 跳转到登录页
    window.location.hash = '/login';
    throw error;
  }
};

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 检查业务错误码
    const { data } = response;
    if (data && data.code === -100) {
      // 身份验证会话已过期
      console.warn('Session expired:', data.msg);
      clearTokens();
      // 跳转到登录页
      window.location.hash = '/login';
      // 创建一个自定义错误对象
      const sessionError = new Error(data.msg || '身份验证会话已过期');
      sessionError.code = -1;
      sessionError.isSessionExpired = true;
      return Promise.reject(sessionError);
    }

    // 请求成功，直接返回数据
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // 如果是401错误且不是刷新token的请求
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // 如果正在刷新token，将请求加入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return request(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newToken = await refreshAccessToken();
        processQueue(null, newToken);

        // 重新发送原始请求
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return request(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);
        clearTokens();
        // 跳转到登录页
        window.location.hash = '/login';
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // 其他错误处理
    const errorMessage =
      error.response?.data?.message || error.message || '请求失败';

    // 可以在这里添加全局错误提示
    console.error('Request Error:', errorMessage);

    // 如果是会话过期错误，清除登录状态并跳转到登录页
    if (error.isSessionExpired || error.code === -1) {
      localStorage.removeItem('userStatus');
      if (window.location.hash.replace(/[#/]/g, '') !== 'login') {
        window.location.hash = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// 封装常用的请求方法
const api = {
  // GET请求
  get: (url, params = {}, config = {}) => {
    return request.get(url, { params, ...config });
  },

  // POST请求
  post: (url, data = {}, config = {}) => {
    return request.post(url, data, config);
  },

  // PUT请求
  put: (url, data = {}, config = {}) => {
    return request.put(url, data, config);
  },

  // DELETE请求
  delete: (url, config = {}) => {
    return request.delete(url, config);
  },

  // PATCH请求
  patch: (url, data = {}, config = {}) => {
    return request.patch(url, data, config);
  },

  // 上传文件
  upload: (url, formData, config = {}) => {
    return request.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    });
  },

  // 下载文件
  download: (url, params = {}, config = {}) => {
    return request.get(url, {
      params,
      responseType: 'blob',
      ...config,
    });
  },
};

// 导出工具函数
export {
  getAccessToken,
  getRefreshToken,
  setTokens,
  clearTokens,
  refreshAccessToken,
  baseUrl,
};

// 默认导出api对象
export default api;
