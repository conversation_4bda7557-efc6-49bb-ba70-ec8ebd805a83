import { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Link,
  Drawer,
  Descriptions,
  Divider,
  Tabs,
  Typography,
  Timeline,
  Modal,
  Badge,
  Form,
  Message,
  Spin,
} from '@arco-design/web-react';
import {
  IconSearch,
  IconPlus,
  IconCalendar,
  IconUser,
  IconUnorderedList,
  IconHistory,
  IconEmail,
} from '@arco-design/web-react/icon';
import request, { baseUrl } from '@/utils/request';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import TimelineItem from '@arco-design/web-react/es/Timeline/item';
import FormItem from '@arco-design/web-react/es/Form/form-item';

const { RangePicker } = DatePicker;

type DynamicObject = {
  [key: string]: any;
};

const getInvoiceTypeTag = (type) => {
  const typeMap = {
    normal: { text: '普通发票', color: 'gray' },
    special: { text: '增值税专票', color: 'orangered' },
  };
  const config = typeMap[type] || { text: type, color: 'gray' };
  return <Tag color={config.color}>{config.text}</Tag>;
};

const getInvoiceStateTag = (state) => {
  const stateMap = {
    success: { text: '已开票', color: 'green' },
    cancelled: { text: '已作废', color: 'red' },
    new: { text: '待开票', color: 'gold' },
    fail: { text: '失败', color: 'red' },
  };
  const config = stateMap[state] || { text: state, color: 'gray' };
  return <Tag color={config.color}>{config.text}</Tag>;
};

const getInvoiceKindBadge = (kind) => {
  const kindMap = {
    blue: { text: '蓝票', color: 'processing' },
    red: { text: '红票', color: 'error' },
  };
  const config = kindMap[kind] || { text: kind, color: 'gray' };
  return <Badge status={config.color} text={config.text} />;
};

export default function InvoiceList() {
  const [loading, setLoading] = useState(false);
  const [historyOrderLoading, setHistoryOrderLoading] = useState(false);
  const [data, setData] = useState([]);
  const [keyword, setKeyword] = useState('');
  const [state, setState] = useState('');
  const [dateRange, setDateRange] = useState([]);
  const [infoVisible, setInfoVisible] = useState(false);
  const [rowSelection, setRowSelection] = useState<DynamicObject>({});
  const [previewVisible, setPreviewVisible] = useState(false);
  const [reopenVisible, setReopenVisible] = useState(false);
  const [form] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [invoiceType, setInvoiceType] = useState('normal');
  const [reopenLoading, setReopenLoading] = useState(false);
  const [titleOptions, setTitleOptions] = useState([]);
  const [fetching, setFetching] = useState(false);
  const [companys, setCompanys] = useState([]);
  const [historyOrderData, setHistoryOrderData] = useState([]);
  const [historyOrderSearchKey, setHistoryOrderSearchKey] = useState('');
  const [historyOrderVisible, setHistoryOrderVisible] = useState(false);
  const [selectedHistoryOrder, setSelectedHistoryOrder] = useState([]);
  const [openInvoiceFlag, setOpenInvoiceFlag] = useState('reopen');
  const [sendEmailVisible, setSendEmailVisible] = useState(false);
  const [sendEmail, setSendEmail] = useState('');
  const [pdfUrl, setPdfUrl] = useState({
    previewUrl: '',
    downloadUrl: '',
  });
  const [pagination, setPagination] = useState({
    total: 0,
    pageSize: 10,
    current: 1,
  });

  const fetchData = () => {
    setLoading(true);
    request
      .get('/order/list', {
        keyword,
        state,
        start_date: dateRange[0],
        end_date: dateRange[1],
        ...pagination,
      })
      .then((res) => {
        setData(res.data.data);
        setPagination({
          ...pagination,
          total: res.data.total,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchHistoryOrderData = () => {
    setLoading(true);
    request
      .get('/order/historyList', {
        keyword: historyOrderSearchKey
      })
      .then((res) => {
        setHistoryOrderData(res.data.data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const searchTitle = (inputValue) => {
    setFetching(true);
    setTitleOptions([]);
    request.get('/invoice/queryCompany', { keyword: inputValue })
      .then((res) => {
        if (res.data.code == 0) {
          setCompanys(res.data.data);
          setTitleOptions(res.data.data.map((item) => {
            return {
              label: item.nsrmc,
              value: item.nsrmc,
            };
          }));
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setFetching(false);
      });
  }

  const columns = [
    {
      title: '订单信息',
      dataIndex: 'order_no',
      key: 'order_no',
      width: 180,
      render: (_, record) => {
        return (
          <Space direction="vertical">
            <div>{record.order_no}</div>
            <div style={{ fontSize: 12, color: '#999' }}>
              {record.name} {record.phone}
            </div>
          </Space>
        );
      },
    },
    {
      title: '金额',
      dataIndex: 'formatted_total_amount',
      key: 'formatted_total_amount',
      width: 120,
      render: (value) => `¥${value}`,
    },
    {
      title: '发票类型',
      dataIndex: 'invoice',
      key: 'invoice_type',
      width: 90,
      render: (invoice) => {
        return invoice ? getInvoiceTypeTag(invoice.invoice_type) : '-';
      },
    },
    {
      title: '发票抬头',
      dataIndex: 'invoice',
      key: 'invoice_title',
      render: (invoice) => {
        return invoice ? invoice.title : '-';
      },
    },
    {
      title: '发票号码',
      dataIndex: 'invoice',
      key: 'invoice_no',
      render: (invoice) => {
        return invoice ? invoice.invoice_no : '-';
      },
    },
    {
      title: '开票日期',
      dataIndex: 'invoice',
      key: 'invoice_date',
      width: 180,
      render: (invoice) => {
        return invoice ? invoice.invoice_date : '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state, record) => {
        if (record?.invoice?.type === 'red') {
          state = 'cancelled';
        }
        return getInvoiceStateTag(state);
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 170,
      render: (_, record) => (
        <Space direction='vertical' align='end'>
          <Space key="action">
            <Link
              key="view"
              disabled={
                !record?.invoice?.invoice_no ||
                !record?.invoice?.invoice_url?.pdfUrl
              }
              onClick={() => {
                setPreviewVisible(true);
                setRowSelection(record);
                setPdfUrl({
                  previewUrl: `${baseUrl}/file/invoice/${record.invoice.invoice_no}.pdf`,
                  downloadUrl: record.invoice.invoice_url.pdfUrl,
                });
              }}
            >
              查看
            </Link>
            <Link
              key="info"
              onClick={() => {
                setInfoVisible(true);
                setRowSelection(record);
              }}
            >
              详情
            </Link>
            <Link
              key="reopen"
              disabled={record?.state !== 'success' ||
                record?.invoice?.state !== 'success' ||
                record?.invoice?.type !== 'blue'
              }
              onClick={() => {
                setOpenInvoiceFlag('reopen');
                setReopenVisible(true);
                setRowSelection(record);
                // 初始化表单状态
                setInvoiceType('normal');
                form.resetFields();
              }}
            >
              换开
            </Link>
          </Space>
          <Space>
            <Link
              key="send-email"
              disabled={
                !record?.invoice?.invoice_no ||
                !record?.invoice?.invoice_url?.pdfUrl
              }
              onClick={() => {
                setRowSelection(record);
                setSendEmailVisible(true);
              }}
            >
              发送邮箱
            </Link>
          </Space>
        </Space>
      ),
    },
  ];

  const handleDirectPrint = (url) => {
    const existingIframe = document.getElementById('print-iframe') as HTMLIFrameElement | null;
    if (existingIframe) {
      // 如果已存在，则重用它
      existingIframe.src = url;
      existingIframe.onload = () => {
        try {
          existingIframe.contentWindow.print();
        } catch (error) {
          console.error('打印失败:', error);
          window.open(url, '_blank');
        }
      };
      return;
    }

    // 创建一个隐藏的 iframe 用于打印
    const iframe = document.createElement('iframe');
    iframe.id = 'print-iframe'; // 添加 ID 以便后续引用
    iframe.style.position = 'fixed';
    iframe.style.right = '-9999px'; // 放在屏幕外而不是 display:none，避免某些浏览器的打印问题
    iframe.style.bottom = '0';
    iframe.style.width = '0';
    iframe.style.height = '0';
    iframe.style.border = '0';
    iframe.src = url;

    document.body.appendChild(iframe);

    iframe.onload = () => {
      try {
        iframe.contentWindow.print();
      } catch (error) {
        console.error('打印失败:', error);
        window.open(url, '_blank');
      }
    };
  };

  useEffect(() => {
    fetchData();
    return () => {
      const printIframe = document.getElementById('print-iframe');
      if (printIframe) {
        document.body.removeChild(printIframe);
      }
    };
  }, [pagination.current]);

  const handleModalClose = () => {
    setPreviewVisible(false);
    const printIframe = document.getElementById('print-iframe');
    if (printIframe) {
      document.body.removeChild(printIframe);
    }
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入单号或姓名手机"
              prefix={<IconSearch />}
              onChange={(value) => setKeyword(value)}
              onPressEnter={() => {
                fetchData();
              }}
              allowClear
              style={{ width: 200 }}
            />
            <Select
              placeholder="请选择状态"
              style={{ width: 120 }}
              allowClear
              onChange={(value) => setState(value)}
              options={[
                { label: '已开票', value: 'success' },
                { label: '已作废', value: 'cancelled' },
                { label: '待处理', value: 'new' },
              ]}
            />
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={(value) => setDateRange(value)}
            />
            <Button
              type="primary"
              icon={<IconSearch />}
              onClick={() => {
                fetchData();
              }}
            >
              查询
            </Button>
            <Button icon={<IconHistory />} onClick={() => {
              setOpenInvoiceFlag('history');
              setHistoryOrderVisible(true);
            }}>历史单据开票</Button>
          </Space>
        </div>
        <Table
          rowKey="id"
          columns={columns}
          loading={loading}
          data={data}
          pagination={pagination}
          onChange={(pagi) => {
            setPagination({
              ...pagination,
              current: pagi.current,
            });
          }}
        />
      </Card>
      {/* 详情抽屉 */}
      <Drawer
        width="50%"
        title="详情"
        visible={infoVisible}
        onOk={() => {
          setInfoVisible(false);
        }}
        onCancel={() => {
          setInfoVisible(false);
        }}
        footer={null}
      >
        <Descriptions
          colon=""
          title="单据详情"
          column={2}
          labelStyle={{ width: 60 }}
          data={[
            {
              label: '姓名',
              value: rowSelection?.name,
            },
            {
              label: '手机号',
              value: rowSelection?.phone,
            },
            {
              label: '单号',
              value: rowSelection?.order_no,
            },
            {
              label: '金额',
              value: `￥${rowSelection?.formatted_total_amount}`,
            },
            {
              label: '时间',
              value: rowSelection?.created_at,
            },
            {
              label: '状态',
              value: getInvoiceStateTag(rowSelection?.state),
            },
          ]}
        />
        <Divider />
        <Tabs defaultActiveTab="1">
          <TabPane
            key="1"
            title={
              <span>
                <IconCalendar style={{ marginRight: 6 }} />
                发票明细
              </span>
            }
          >
            <Typography.Paragraph>
              <Table
                rowKey="id"
                columns={[
                  {
                    title: '发票种类',
                    dataIndex: 'type',
                    key: 'type',
                    render: (type) => {
                      return getInvoiceKindBadge(type);
                    },
                  },
                  {
                    title: '发票类型',
                    dataIndex: 'invoice_type',
                    key: 'invoice_type',
                    render: (invoice_type) => {
                      return getInvoiceTypeTag(invoice_type);
                    },
                  },
                  {
                    title: '发票号码',
                    dataIndex: 'invoice_no',
                    key: 'invoice_no',
                  },
                  {
                    title: '状态',
                    dataIndex: 'state',
                    key: 'state',
                    render: (state) => {
                      return getInvoiceStateTag(state);
                    },
                  },
                  {
                    title: '操作',
                    key: 'action',
                    width: 120,
                    render: (
                      _,
                      record: {
                        invoice_no: string;
                        invoice_url: { pdfUrl: string };
                      }
                    ) => (
                      <Space>
                        <Link
                          disabled={
                            !record?.invoice_url?.pdfUrl || !record?.invoice_no
                          }
                          onClick={() => {
                            setPdfUrl({
                              previewUrl: `${baseUrl}/file/invoice/${record.invoice_no}.pdf`,
                              downloadUrl: record.invoice_url.pdfUrl,
                            });
                            setPreviewVisible(true);
                          }}
                        >
                          查看
                        </Link>
                        <Link
                          href={record?.invoice_url?.pdfUrl}
                          disabled={!record?.invoice_url?.pdfUrl}
                        >
                          下载
                        </Link>
                      </Space>
                    ),
                  },
                ]}
                data={rowSelection?.invoices}
                pagination={false}
                size="small"
              />
            </Typography.Paragraph>
          </TabPane>
          <TabPane
            key="2"
            title={
              <span>
                <IconUnorderedList style={{ marginRight: 6 }} />
                订单明细
              </span>
            }
          >
            <Typography.Paragraph>
              <Table
                rowKey="id"
                columns={[
                  {
                    title: '商品名称',
                    dataIndex: 'article_name',
                    key: 'article_name',
                  },
                  {
                    title: '单位',
                    dataIndex: 'unit',
                    key: 'unit',
                  },
                  {
                    title: '数量',
                    dataIndex: 'num',
                    key: 'num',
                  },
                  {
                    title: '金额',
                    dataIndex: 'amount',
                    key: 'amount',
                  },
                ]}
                data={rowSelection?.detail}
                pagination={false}
                size="small"
                stripe
              />
            </Typography.Paragraph>
          </TabPane>
          <TabPane
            key="3"
            title={
              <span>
                <IconUser style={{ marginRight: 6 }} />
                操作记录
              </span>
            }
          >
            <Typography.Paragraph>
              <Timeline mode="left" labelPosition="relative">
                {rowSelection?.logs?.map((item) => {
                  return (
                    <TimelineItem
                      key={item.id}
                      label={item.formatted_created_at}
                    >
                      <div style={{ marginBottom: 12 }}>
                        {item.info}
                        <div style={{ fontSize: 12, color: '#4E5969' }}>
                          操作人：
                          {item.operator_id == 0
                            ? '系统'
                            : item?.operator?.name}
                        </div>
                      </div>
                    </TimelineItem>
                  );
                })}
              </Timeline>
            </Typography.Paragraph>
          </TabPane>
        </Tabs>
      </Drawer>
      {/* 查看发票 */}
      <Modal
        key="preview"
        title="发票预览"
        visible={previewVisible}
        unmountOnExit
        style={{ width: '860px', maxWidth: '1400px' }}
        footer={
          <>
            <Button
              onClick={() => {
                window.location.href = pdfUrl.downloadUrl;
              }}
            >
              下载
            </Button>
            <Button
              onClick={() => {
                handleDirectPrint(pdfUrl.previewUrl);
              }}
              type="primary"
            >
              打印
            </Button>
          </>
        }
        onCancel={handleModalClose}
      >
        <object
          data={pdfUrl.previewUrl + '#toolbar=0'}
          type="application/pdf"
          width="100%"
          height="540px"
          style={{ border: 'none' }}
        >
          <p>
            PDF 加载失败，请
            <a href={pdfUrl.downloadUrl}>下载 PDF</a>
            查看。
          </p>
        </object>
      </Modal>
      {/* 重开发票 */}
      <Modal
        key="reopen"
        title="填写开票信息"
        visible={reopenVisible}
        unmountOnExit
        style={{ width: '860px', maxWidth: '1400px' }}
        footer={
          <>
            <Button
              onClick={() => {
                setReopenVisible(false);
                form.resetFields();
                setInvoiceType('normal');
              }}
            >
              取消
            </Button>
            <Button
              loading={reopenLoading}
              onClick={() => {
                form
                  .validate()
                  .then((values) => {
                    setReopenLoading(true);
                    const formData = values;
                    let url = '';
                    if (openInvoiceFlag === 'reopen') {
                      url = '/order/reopenInvoice';
                      formData.id = rowSelection.id;
                    } else {
                      url = '/order/openHistoryInvoice';
                      formData.order_no = selectedHistoryOrder[0];
                    }
                    request
                      .post(url, formData)
                      .then((res) => {
                        if (res.data.code === 0) {
                          setReopenVisible(false);
                          form.resetFields();
                          setInvoiceType('normal');
                          fetchData();
                          Message.success('提交成功');
                        } else {
                          Message.error(res.data.msg);
                        }
                        setReopenLoading(false);
                      });
                  })
                  .catch((error) => {
                    setReopenLoading(false);
                    console.log('表单验证失败:', error);
                  })
                  .finally(() => {
                    setReopenLoading(false);
                  });
              }}
              type="primary"
            >
              确定
            </Button>
          </>
        }
        onCancel={() => {
          setReopenVisible(false);
          form.resetFields();
          setInvoiceType('normal');
        }}
      >
        <Form form={form} layout="vertical">
          <style>
            {`
              .arco-form-item {
                margin-bottom: 10px !important;
              }
            `}
          </style>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '0px 16px',
              rowGap: '8px',
            }}
          >
            <FormItem
              label="发票类型"
              field="invoice_type"
              rules={[{ required: true, message: '请选择发票类型' }]}
            >
              <Select
                placeholder="请选择发票类型"
                value={invoiceType}
                onChange={(value) => {
                  setInvoiceType(value);
                  // 当切换发票类型时，清空其他字段的值
                  form.setFieldsValue({
                    tax_no: '',
                    bank: '',
                    bank_account: '',
                    address: '',
                    tel: '',
                    email: '',
                  });
                }}
                options={[
                  { label: '普通发票', value: 'normal' },
                  { label: '增值税专票', value: 'special' },
                ]}
              />
            </FormItem>
            <FormItem
              label="发票抬头"
              field="title"
              rules={[{ required: true, message: '请输入发票抬头' }]}
            >
              {/* {invoiceType === 'normal' ? (
                <Input placeholder="请输入发票抬头" />
              ) : ( */}
              <Select
                showSearch
                options={titleOptions}
                placeholder='请输入发票抬头'
                filterOption={false}
                allowCreate={true}
                allowClear={true}
                notFoundContent={
                  fetching ? (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Spin style={{ margin: 12 }} />
                    </div>
                  ) : null
                }
                onSearch={(value) => {
                  if (value) {
                    form.setFieldValue('title', value);
                  }
                  searchTitle(value);
                }}
                onChange={(value) => {
                  if (!value) return;

                  const company = companys.find((item) => item.nsrmc === value);
                  if (company) {
                    form.setFieldsValue({
                      title: company.nsrmc,
                      tax_no: company.nsrsbh,
                      address: company.dz,
                      bank: company.khhmc,
                      bank_account: company.yhzh,
                      tel: company.lxdh,
                    });
                  }
                }}
              />
              {/* )} */}
            </FormItem>
            <FormItem
              label="税号"
              field="tax_no"
              rules={
                invoiceType === 'special'
                  ? [{ required: true, message: '请输入税号' }]
                  : []
              }
            >
              <Input
                placeholder="请输入税号"
              // disabled={invoiceType === 'normal'}
              />
            </FormItem>
            <FormItem
              label="地址"
              field="address"
              rules={
                invoiceType === 'special'
                  ? [{ required: true, message: '请输入地址' }]
                  : []
              }
            >
              <Input
                placeholder="请输入地址"
              // disabled={invoiceType === 'normal'}
              />
            </FormItem>
            <FormItem
              label="开户行"
              field="bank"
              rules={
                invoiceType === 'special'
                  ? [{ required: true, message: '请输入开户行' }]
                  : []
              }
            >
              <Input
                placeholder="请输入开户行"
              // disabled={invoiceType === 'normal'}
              />
            </FormItem>
            <FormItem
              label="银行账号"
              field="bank_account"
              rules={
                invoiceType === 'special'
                  ? [{ required: true, message: '请输入银行账号' }]
                  : []
              }
            >
              <Input
                placeholder="请输入银行账号"
              // disabled={invoiceType === 'normal'}
              />
            </FormItem>
            <FormItem
              label="联系电话"
              field="tel"
              rules={
                invoiceType === 'special'
                  ? [{ required: true, message: '请输入联系电话' }]
                  : []
              }
            >
              <Input
                placeholder="请输入联系电话"
              // disabled={invoiceType === 'normal'}
              />
            </FormItem>
            <FormItem
              label="邮箱地址"
              field="email"
              tooltip="用于接收发票通知，不填则不发送通知"
            >
              <Input placeholder="请输入邮箱地址" />
            </FormItem>
          </div>
          <FormItem label="备注" field="remark" style={{ marginTop: '16px' }}>
            <Input.TextArea placeholder="请输入备注" rows={3} />
          </FormItem>
        </Form>
      </Modal>
      {/* 历史单据开票 */}
      <Modal
        key="history"
        title="历史单据开票"
        visible={historyOrderVisible}
        unmountOnExit
        style={{ width: '700px', maxWidth: '1400px' }}
        onCancel={() => {
          setHistoryOrderVisible(false);
          setHistoryOrderSearchKey('');
          setHistoryOrderData([]);
          setSelectedHistoryOrder([]);
        }}
        onOk={() => {
          console.log(selectedHistoryOrder);
          if (selectedHistoryOrder.length === 0) {
            Message.error('请选择订单');
            return;
          }
          setReopenVisible(true);
        }}
      >
        <Space direction='vertical' style={{ width: '100%' }} size='medium'>
          <Space style={{ width: '100%' }}>
            <Input
              placeholder="请输入hisid、手机号或零售单号"
              prefix={<IconSearch />}
              onChange={(value) => setHistoryOrderSearchKey(value)}
              onPressEnter={() => {
                fetchHistoryOrderData();
              }}
              allowClear
              style={{ width: '570px' }}
            />
            <Button
              type="primary"
              icon={<IconSearch />}
              onClick={() => {
                setSelectedHistoryOrder([]);
                fetchHistoryOrderData();
              }}
            >
              查询
            </Button>
          </Space>
          <Table
            rowKey="c_retailcode"
            size='small'
            stripe
            indentSize={60}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectedHistoryOrder,
              onChange: (selectedRowKeys) => {
                setSelectedHistoryOrder(selectedRowKeys);
              },
            }}
            columns={[
              {
                title: '订单号',
                dataIndex: 'c_retailcode',
                key: 'c_retailcode',
              },
              {
                title: '病历号',
                dataIndex: 'client_his_id',
                key: 'client_his_id',
              },
              {
                title: '客户名称',
                dataIndex: 'client_name',
                key: 'client_name',
              },
              {
                title: '电话',
                dataIndex: 'client_phone',
                key: 'client_phone',
              },
            ]}
            loading={historyOrderLoading}
            data={historyOrderData}
            expandedRowRender={(row) => {
              return <Table key={row.c_retailcode + row.art_breedname} border columns={
                [
                  {
                    title: '品名',
                    dataIndex: 'art_breedname',
                    key: 'art_breedname'
                  },
                  {
                    title: '数量',
                    dataIndex: 'art_number',
                    key: 'art_number'
                  },
                  {
                    title: '金额',
                    dataIndex: 'art_zprice_amount',
                    key: 'art_zprice_amount'
                  },
                  {
                    title: '优惠',
                    dataIndex: 'art_agio_amount',
                    key: 'art_agio_amount'
                  }
                ]
              } data={row.list} pagination={false} />
            }}
          />
        </Space>
      </Modal>
      {/* 发送邮箱 */}
      <Modal
        title='发送至邮箱'
        visible={sendEmailVisible}
        onOk={() => {
          // 添加表单验证
          emailForm.validate().then((values) => {
            request.post('/invoice/sendInvoice', {
              id: rowSelection.invoice.id,
              email: values.email
            }).then((res) => {
              if (res.data.code === 0) {
                Message.success('发送成功');
                setSendEmailVisible(false);
                setSendEmail('');
                emailForm.resetFields();
              } else {
                Message.error(res.data.msg);
              }
            });
          }).catch((error) => {
            console.log('邮箱验证失败:', error);
          });
        }}
        onCancel={() => {
          setSendEmailVisible(false);
          setSendEmail('');
          emailForm.resetFields();
        }}
        autoFocus={false}
        focusLock={true}
      >
        <Form form={emailForm} layout="vertical">
          <FormItem
            field="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              {
                type: 'email',
                message: '请输入正确的邮箱格式'
              }
            ]}
          >
            <Input
              placeholder="请输入邮箱"
              prefix={<IconEmail />}
              onChange={(value) => setSendEmail(value)}
              onPressEnter={() => {
                emailForm.validate().then(() => {
                  console.log('回车提交邮箱');
                });
              }}
              allowClear
            />
          </FormItem>
        </Form>
      </Modal>
    </div >
  );
}
